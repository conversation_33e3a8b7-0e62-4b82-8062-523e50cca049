import enum
from pathlib import Path
from tempfile import gettemp<PERSON>
from typing import Literal

from pydantic import SecretStr, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from yarl import URL

TEMP_DIR = Path(gettempdir())


class LogLevel(enum.StrEnum):
    """Possible log levels."""

    NOTSET = "NOTSET"
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    FATAL = "FATAL"


# ruff: noqa: S105
class Settings(BaseSettings):
    """Application settings.

    NOTE: These parameters can be and will likely be overriden by AWS Parameter Store values.
    """

    # quantity of workers for uvicorn
    workers_count: int = 1
    # Enable uvicorn reloading
    reload: bool = False
    # Global max concurrency control for a uvicorn worker
    max_concurrent_requests_per_worker: int = 40

    # NOTE: DO NOT SET THIS FLAG IN PARAMETER STORE
    # it will be set in the helm/deploy script
    public_facing_routes_only: bool = False

    default_worker_thread_pool_size: int = 5
    default_main_thread_pool_size: int = 40  # starlette default

    public_base_url: str = "localhost"
    public_app_base_url: str = "localhost"
    host: str = "127.0.0.1"
    port: int = 8000

    # Current environment
    environment: str = "local"
    enable_crm_sync: bool = False
    enable_scheduler_notification_email: bool = False
    reevo_org_ids: list[str] = []
    organization_ids_without_cdc_event_proc: list[str] = []

    log_level: LogLevel = LogLevel.INFO
    # Variables for the database
    db_host: str = "localhost"
    db_port: int = 5432
    db_user: str = "salestech_be"
    db_pass: str = "salestech_be"
    db_migrator_user: str = "salestech_be"
    db_migrator_pass: str = "salestech_be"
    db_base: str = "salestech_be"
    db_echo: bool = False
    db_pool_size: int = 20
    db_max_overflow: int = 5
    db_conn_prewarm: bool = False

    # Variables for PostgreSQL Data Warehouse
    dwh_host: str = "localhost"
    dwh_port: int = 5432
    dwh_user: str = "salestech_be"
    dwh_pass: str = "salestech_be"
    dwh_base: str = "salestech_be_dwh"
    dwh_echo: bool = False
    dwh_pool_size: int = 20
    dwh_max_overflow: int = 5

    # Variables for FalkorDB
    falkordb_host: str = "localhost"
    falkordb_port: int = 6380
    falkordb_user: str | None = None
    falkordb_password: str | None = None
    falkordb_blocking_connection_pool: bool = True
    falkordb_max_connections: int = 16
    falkordb_graph_prefix: str = "org_"

    # Variables for Redis
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_user: str | None = None
    redis_pass: str | None = None
    redis_base: int | None = None

    # Variables for S3 buckets
    aws_region: str = "us-west-2"
    transcript_bucket_name: str = "reevo-dev-ng-meeting-transcripts-bucket"
    meeting_bucket_name: str = "reevo-dev-ng-meeting-videos-bucket"
    email_attachment_bucket_name: str = "reevo-dev-ng-email-attachments-bucket"
    import_csv_bucket_name: str = "reevo-dev-ng-import-csv-bucket"
    avatar_bucket_name: str = "reevo-dev-avatar-bucket"
    reevo_public_s3_bucket_name: str = "reevo-public"
    call_recording_bucket_name: str = "reevo-dev-call-recordings-bucket"
    call_recording_v2_bucket_name: str = "reevo-dev-call-recordings-v2-bucket"
    voicemail_greetings_bucket_name: str = (
        "reevo-dev-personalized-voicemail-greetings-bucket"
    )
    research_ingested_bucket_name: str = "reevo-dev-research-ingested-bucket"

    # Variables for MSK
    msk_enabled: bool = False  # use local Kafka when msk_enabled=False
    msk_bootstrap_servers: list[str] = []
    msk_consumer_group_id: str = "default"
    msk_username: str = "salestech-be"
    msk_password: SecretStr = SecretStr("placeholder")
    msk_output_raw_message: bool = False
    msk_max_poll_records: int | None = None
    msk_session_timeout_ms: int = 10000
    msk_heartbeat_interval_ms: int = 3000

    # Variables for Kafka
    kafka_bootstrap_servers: list[str] = ["localhost:9094", "salestech_be-kafka:9092"]
    kafka_consumer_group_id: str = "default"

    # This variable is used to define
    # multiproc_dir. It's required for [uvi|guni]corn projects.
    prometheus_dir: Path = TEMP_DIR / "prom"

    # OpenAI API Key
    openai_api_key: SecretStr = SecretStr(
        "***************************************************",
    )
    openai_research_agent_key: SecretStr = SecretStr(
        "***********************************************************************************************************************************************"
    )
    openai_organization_id: str = "org-KMsSnNpd9E9KfXYEymVoWG7s"

    anthropic_api_key: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")

    # Vertex AI Service Account Credentials JSON
    # https://docs.litellm.ai/docs/providers/vertex
    vertex_ai_cred_json: SecretStr = SecretStr(
        """{
  "type": "*****",
  "project_id": "*****",
  "private_key_id": "*****",
  "private_key": "*****",
  "client_email": "*****",
  "client_id": "*****",
  "auth_uri": "*****",
  "token_uri": "*****",
  "auth_provider_x509_cert_url": "*****",
  "client_x509_cert_url": "*****",
  "universe_domain": "*****"
}"""
    )

    # SendGrid integration
    sendgrid_api_key: str = (
        "*********************************************************************"
    )

    # Twilio integration
    twilio_global_account_sid: str = "SETUP_IN_DOT_ENV_FOR_LOCAL"
    twilio_global_auth_token: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")
    twilio_inbound_call_forwarding_enabled: bool = False
    twilio_inbound_call_voicemail_message: str = (
        "No one is available to take your call. Please leave a message after the beep."
    )
    twilio_inbound_removed_phone_number_message: str = (
        "The number {to_number} you have reached is no longer in service, good bye."
    )
    twilio_inbound_call_fowarding_timeout: int = 15
    twilio_voice_recording_mode: str = (
        "record-from-answer-dual"  # dual mode provides metadata for speakers/channels
    )
    twilio_voicemail_silence_timeout: int = 15
    twilio_voicemail_max_length: int = 60
    twilio_voicemail_max_size: int = 10 * 1024 * 1024
    twilio_phone_number_release_grace_period: int = 14
    voice_v2_assembly_ai_transcribe_model: str = "best"
    voice_enable_account_id_and_activity_association: bool = False
    voice_unknown_caller_name: str = "unknown"
    voice_enable_notifications: bool = False
    voice_quota_limit: dict[str, int] = {
        "voice_call_second": 60000,
        "phone_number_update": 1,
        "total_phone_line": 1,
    }
    # Grpc endpoint for opentelemetry.
    # E.G. http://localhost:4317
    opentelemetry_endpoint: str | None = None

    # Meeting
    enable_async_scheduler_meeting_create: bool = False
    enable_insight_task_creation: bool = False
    enable_insights_job_invocation: bool = False
    enable_updated_assembly_analysis: bool = False
    enable_recall_call_events: bool = False
    enable_recall_signature_verification: bool = False
    enable_recall_recording_mode_options: bool = False
    enable_meeting_timing_job: bool = False
    enable_google_meet_authenticated_bot: bool = False
    enable_recall_register_map_zoom_user_credential: bool = False
    enable_new_recall_process_transcript: bool = False
    check_status_for_bot_update: bool = False
    enable_nylas_user_calendar_webhook_handle_in_temporal: bool = False
    enable_zoom_bot_join: bool = False
    enable_zoom_bot_join_org_ids: list[str] = [
        "e852cd1c-9e10-4388-96ea-fe908e4b0b80",
        "7e1e5279-4fd4-4f1d-a01f-3aa68a276bed",
    ]
    enable_teams_bot_join: bool = False
    enable_teams_bot_join_org_ids: list[str] = [
        "e852cd1c-9e10-4388-96ea-fe908e4b0b80",
        "7e1e5279-4fd4-4f1d-a01f-3aa68a276bed",
    ]
    enable_patch_meeting_bot_enable_override: bool = False
    enable_patch_meeting_bot_enable_override_org_ids: list[str] = [
        "e852cd1c-9e10-4388-96ea-fe908e4b0b80",
        "7e1e5279-4fd4-4f1d-a01f-3aa68a276bed",
    ]

    # Stage Criteria
    enable_sales_action_role_classification: bool = False
    enable_sales_action_role_classification_org_ids: list[str] = []
    enable_pipeline_update_multi_meeting_run: bool = False
    enable_pipeline_update_multi_meeting_run_org_ids: list[str] = []

    # Realtime transcript
    event_bridge_transcript_name: str = "event_bus_transcript_dev"
    recall_realtime_transcript_enabled: bool = False
    recall_realtime_transcript_webhook_url: str = (
        "https://api.reevo.ai/api/v1/webhook/recallai/bot/event"
    )
    recall_realtime_transcript_provider: str = "assembly_ai"
    recall_realtime_partial_results: bool = True
    recall_realtime_sampling: int = -1
    recall_realtime_logging: bool = False
    recall_timeout: float = 5.0

    # Recall AI configuration
    recall_v1_api_key: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")
    recall_v1_base_url: str = "https://api.recall.ai/api/v1"
    recall_v2_base_url: str = "https://api.recall.ai/api/v2"
    recall_v2_beta_base_url: str = "https://api.recall.ai/api/v2beta"
    recall_webhook_signing_secret: str = "SETUP_IN_DOT_ENV_FOR_LOCAL"
    recall_google_login_group_id: str = "729c576f-bc35-4023-9c37-449d48573d92"
    enable_updated_meeting_sync_all: bool = True
    updated_meeting_sync_org_ids: list[str] = []
    enable_bot_welcome_message_timing_check: bool = True
    recall_default_transcript_provider: str = "speechmatics"

    # self-defined key. we assign this key as a part of Recall callback url.
    recall_zoom_oauth_callback_key: str = "dnh0DkVQL0BizMgBxC09hbTgp"
    recall_zoom_oauth_app_id: str = "e085f792-d183-4c32-8b51-3eb3e5ae19fb"
    recall_oauth_callback_url: str = (
        "https://api-dev.reevo.ai/api/v1/webhook/recall_zoom"
    )

    # pdl configuration
    pdl_api_key: SecretStr = SecretStr("placeholder")
    pdl_preview_search_api_key: SecretStr = SecretStr("placeholder")
    pdl_base_url: str = "https://sandbox.api.peopledatalabs.com/v5/"
    pdl_autocomplete_base_url: str = "https://api.peopledatalabs.com/v5/"
    pdl_autocomplete_min_count: int = 50

    # nylas configuration
    nylas_v3_api_key: SecretStr = SecretStr(
        "nyk_v0_Y0tQpxomkWkEpbviiCG5BRYJF1WPpoYzjS3cUYVdCsYO5f13lWsCaojzeizmxvjS",
    )
    nylas_v3_api_url: str = "https://api.us.nylas.com"

    # !! For local testing only, set this cred-id: f472d479-44e2-4206-9dbf-c32366878fcd
    # to point nylas sandbox environment to our Reevo Dev google App.
    # nylas sandbox api key can be found in 1pw
    nylas_v3_google_app_override_cred_id: str | None = None

    dev_nylas_test_user_id: str = "94d52d20-00cc-48da-8727-8c84c474cb7e"

    # Oauth google
    oauth_google_client_id: str = (
        "726214157994-dlcqugr55r9mbvr54g9rbj896fqs23qu.apps.googleusercontent.com"
    )
    oauth_google_client_secret: str = "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"

    # Reevo Google Client
    connect_google_client_id: str = (
        "899488811076-serpg7b656t2voddplko3p49mi7qj4kt.apps.googleusercontent.com"
    )
    connect_google_client_secret: str = "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"
    connect_google_return_url: str = "https://app.reevo.ai/settings/integrations"

    # Google Drive API key for importing external meeting videos
    import_external_meeting_google_api_key: SecretStr = SecretStr("placeholder")

    # Google Search
    google_api_key: SecretStr = SecretStr("AIzaSyDq5pz-O31eiAn5HC4w-xuhdGMPPb2yfcg")
    google_cse_id: SecretStr = SecretStr("644de9eaada0441b4")

    # Oauth zoom
    oauth_zoom_app_client_id: str = "sPzalhbNQl6xSVinYM5bHg"
    oauth_zoom_app_client_secret: str = "RVVIHgOJGtTdYQ2DoPSzJYFe98FYgc4r"
    oauth_zoom_base_url: str = "https://zoom.us/oauth/authorize"
    zoom_base_url: str = "https://api.zoom.us/v2"
    oauth_zoom_callback_url: str = (
        "https://app.reevo.ai/api/bff/v1/connect/callback/zoom"
        # "http://127.0.0.1:8000/api/v1/connect/callback/zoom"
    )

    # Oauth HubSpot
    hubspot_base_url: str = "https://api.hubapi.com"
    hubspot_app_client_id: SecretStr = SecretStr("placeholder")
    hubspot_app_client_secret: SecretStr = SecretStr(
        "placeholder",
    )

    # Oauth Reevo integration landing page
    reevo_oauth_integration_landing_page_url: str = (
        "https://app.reevo.ai/settings/integrations"
        # "/api/v1/connect/test_only/zoom/home"
    )

    # Auth0
    auth0_domain: str = "reevo.us.auth0.com"
    auth0_issuer: str = "https://reevo.us.auth0.com/"
    auth0_algorithms: list[str] = ["RS256"]
    auth0_app_client_id: str = "wh0qDNItQYiXJmiuE22UZkGTcE1ams7t"
    auth0_app_client_secret: str = (
        "****************************************************************"
    )
    auth0_auth_redirect_uri: str = (
        "https://f1e1-4-78-242-50.ngrok-free.app/api/v1/identity/callback"
    )
    auth0_api_audience: str = "https://api.sales-tech-labs.com/"
    auth0_management_api_domain: str = "reevo.us.auth0.com"  # may not use custom domain
    auth0_management_api_client_id: str = "placeholder"
    auth0_management_api_client_secret: str = "placeholder"

    # Salesforce
    salesforce_client_id: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")
    salesforce_client_secret: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")
    salesforce_crm_user_client_id: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")
    salesforce_crm_user_client_secret: SecretStr = SecretStr(
        "SETUP_IN_DOT_ENV_FOR_LOCAL"
    )
    connect_salesforce_return_url: str = "https://app.reevo.ai/settings/integrations"

    # Microsoft
    microsoft_personal_email_domain: list[str] = [
        "live.com",
        "outlook.com",
        "hotmail.com",
    ]
    oauth_microsoft_client_id: str = "03bfcb64-e56f-43bb-b3a5-fd379207cbdd"
    oath_microsoft_client_secret: str = "****************************************"
    connect_microsoft_return_url: str = (
        # "https://app.reevo.ai/settings/integrations"
        "http://localhost:3000/settings/integrations"
    )

    # Crustdata
    crustdata_base_url: str = "https://api.crustdata.com"
    crustdata_api_key: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")

    # Brightdata
    brightdata_base_url: str = "https://api.brightdata.com"
    brightdata_api_key: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")

    # JWT: reevo issued jwt tokens
    # NOTE: You can use this to generate a secret key: `openssl rand -hex 32`
    jwt_secret: str = "secret key"
    jwt_ttl: int = 604800
    jwt_issuer: str = "http://localhost"
    jwt_audience: str = "http://localhost"
    # NOTE: Use HS256 for now for simplicity and performance (less cpu intensive)
    # We only have "monolith" which is the issuer and the audience for now, so symmetric key is not a concern
    # If in the future we have SoA, where the signing key is shared to multiple (external) services, we can use RS256
    jwt_algorithm: str = "HS256"

    # Initially designed to track token version for updating a user's set of JWT claims.
    # This is used to ensure that the token is up to date before processing a request.
    # If the token is not up to date, the request will be rejected (via 401 Unauthorized).
    # The FE could optionally refresh the token for a seamless user experience (may require additional backend work).
    jwt_minimum_required_token_version: int = 0

    # Task Queue
    task_queue_namespace: str = "salestech_be_tasks"

    # Temporal
    temporal_host_url: str = "localhost:7233"
    temporal_namespace: str = "default"
    temporal_mtls_tls_cert: str | None = None
    temporal_mtls_tls_key: str | None = None

    # Temporal WF
    calendar_worker_concurrent_size: int = 20
    meeting_worker_concurrent_size: int = 5
    email_worker_activity_concurrent_size: int = 20
    email_worker_wf_concurrent_size: int = 20
    email_worker_wf_poll_size: int = 20
    email_worker_activity_poll_size: int = 20
    email_worker_db_connection_pool_size: int = 10
    email_worker_disable_eager_activity: bool = False
    default_worker_concurrent_size: int = 15
    crm_sync_worker_concurrent_size: int = 15
    sequence_worker_concurrent_size: int = 20
    sequence_worker_db_connection_pool_size: int = 10
    prospecting_worker_concurrent_size: int = 20
    domain_crm_association_worker_concurrent_size: int = 20
    # Temporal - Research Features
    research_worker_enable_company_news: bool = False
    research_worker_max_concurrent_wf_tasks: int = 10
    research_worker_max_concurrent_wf_tasks_polls: int = 1
    ## Crustdata
    research_worker_enable_crustdata_person_info: bool = True
    research_worker_enable_crustdata_company_info: bool = True
    ## Brightdata
    ### use Brightdata as failover solution when Crustdata is down
    research_worker_force_use_brightdata_for_company_info: bool = False
    research_worker_force_use_brightdata_for_person_info: bool = False
    research_worker_force_use_brightdata_for_linkedin_posts: bool = False

    # External s3 share IAM user access cred
    s3_external_share_user_access_key_id: str = "placeholder"
    s3_external_share_user_access_key_secret: str = "placeholder"

    # Sequence

    # Quota Policy
    quota_policy_core_plan_max_active_users: int = 5

    # Quota Policy - Domains and Mailboxes
    quota_per_org_max_mailboxes: int = 5
    quota_per_user_max_mailboxes: int = 2
    quota_per_org_plan_included_mailboxes: int = 2
    quota_per_org_max_domains: int = 2
    quota_per_org_plan_included_domains: int = 1

    # Domain Costs
    max_cost_per_plan_included_domain: int = 20_00  # this is the highest amount that we cover for the customer, equivalent to $20
    max_cost_per_invoiced_domain: int = 100_00  # represents the max that we automatically invoice, equivalent to $100, anything above contact us

    # Domain Length Values
    domain_sld_max_len: int = (
        63  # this is a DNS specification and should never be modified
    )
    domain_sld_min_len: int = 3  # this is self-imposed by us

    # CRM Integrity
    crm_integrity_max_concurrent_running_jobs: int = 5

    # ---------------
    #  OBSERVABILITY
    # ---------------

    # Sentry's configuration
    sentry_dsn: str | None = None
    sentry_traces_sample_rate: float = 0  # capture xx% of transactions
    sentry_profiles_sample_rate: float = 0  # profile xx% of transactions

    sentry_traces_sample_rate_temporal_worker_research: float = 1.0
    # NOTE: start with full profiling and reduce as needed
    sentry_profiles_sample_rate_temporal_worker_research: float = 1.0
    sentry_auto_profiling_temporal_worker_research: bool = True

    # Datadog configurations
    datadog_on: bool = False
    dd_agent_host: str = "localhost"
    dd_agent_statsd_port: int = 8125
    dd_agent_grpc_port: int = 4317
    # ... other dd_agent config or ports

    # ---------------
    #  SECURITY
    # ---------------

    # key for data-at-rest encryption
    fernet_keys: list[str] = [
        "WF8aMXrld-6aXQxwMTKC95ZMeMgzyYiIwPVbT9hYA7c=",
        "k-3Cj5xQ-HtmklbNyhJx0Ns9LlwfXALgXBS2byS-4Ak=",
    ]

    # Session data encryption
    session_secret_key: str = "random-string"

    # Allowed redirect url domains, e.g. for those connect integration callback/redirects
    allowed_redirect_url_domains: list[str] = ["reevo.ai"]

    assembly_ai_api_key: SecretStr = SecretStr("6f279356a6ea4b2da0edc6c8ad4a2cd3")

    voyage_api_key: SecretStr = SecretStr("an-api-key")
    voyage_text_model: str = "voyage-3"
    infraforge_api_url: str = "https://api.infraforge.ai/public"
    infraforge_api_key: SecretStr = SecretStr("placeholder")
    mailivery_api_url: str = "https://app.mailivery.io/api/v1/campaigns"
    mailivery_api_key: SecretStr = SecretStr("placeholder")

    # event (open-rate, link-click) tracking encryption keys.
    # NOTE: add new keys to the BEGINNING of the list as we are utilizing multi-fernet.
    # the way this works is the first key is always used for encryption.
    # for decryption, they are tried in order until one works.
    # NOTE: event_tracking_base_url will be replaced by event_tracking_default_host in the future
    event_tracking_base_url: str = "http://localhost:8000/e"
    event_tracking_default_host: str = "localhost:8000"
    event_tracking_fernet_keys: list[str] = [
        "Ebxn6Ltm-a3DfvluVTrdoODxQrwD5_DwLGUEN3QfHbE=",  # added 2025-04-01.
    ]
    enable_process_email_event_tracking: bool = False
    # ---------------
    #  Performance
    # ---------------
    profile_nylas_webhook_enabled: bool = False

    # ---------------
    #  Feature Flags
    # ---------------

    enable_answering_machine_detection: bool = False
    enable_transcript_recall_custom_words: bool = False
    enable_research_agent_on_person: bool = False
    enable_research_agent_on_company: bool = False
    enable_research_agent_on_meeting: bool = False
    enable_contact_research_enrichment: bool = True
    enable_pipeline_intel_on_meeting: bool = True
    enable_sequence_exit_on_meeting: bool = False
    enable_existing_insight_check: bool = True
    enable_purchased_domain_cname: bool = False
    enable_purchased_domain_cname_org_ids: list[str] = []
    new_feature_org_ids: list[str] = [
        "b3c5bc5d-1eae-4586-b9bd-db8486e6b689",
        "4d29f892-7e25-4efa-ad0b-f348bd0fc0fc",
        "cce7b290-8a08-4904-a6c7-2b6613877cf5",
        "7e1e5279-4fd4-4f1d-a01f-3aa68a276bed",
    ]

    enable_approval_requirements: bool = False

    enable_new_domain_payments: bool = False
    enable_new_domain_payments_org_ids: list[str] = []
    enable_slack_invoice: bool = False
    enable_slack_invoice_org_ids: list[str] = []
    enable_slack_domain_purchase_alert: bool = False
    enable_slack_domain_purchase_alert_org_ids: list[str] = []

    enable_stopgap_pipeline_validation: bool = True

    hubspot_sync_orgs_json: list[str] = []

    enable_task_due_date: bool = False
    enable_task_priority: bool = False

    enable_mailbox_perms: bool = False
    enable_prospecting_perms: bool = False
    enable_sequence_perms: bool = False
    enable_sequence_perms_org_ids: list[str] = []

    enable_sequence_mail_delivery_delay: bool = False
    enable_sequence_mail_delivery_delay_orgs: list[str] = []
    enable_sequence_enrollment_wf_v2: bool = False
    enable_sequence_enrollment_wf_v2_orgs: list[str] = []
    enable_sequence_delivery_window_v2: bool = False
    enable_sequence_delivery_window_v2_orgs: list[str] = []

    enable_domain_health_check_per_domain: bool = False

    enable_email_body_llm_parsing: bool = False
    enable_langfuse_for_email_insights: bool = False
    enable_langfuse_for_meeting_insights: bool = False
    enable_email_classification: bool = False
    enable_generate_objections_from_conversation: bool = False
    enable_generate_tasks_from_objection_insights: bool = False
    enable_generate_tasks_from_objection_insights_org_ids: list[str] = [
        "e852cd1c-9e10-4388-96ea-fe908e4b0b80",
        "7e1e5279-4fd4-4f1d-a01f-3aa68a276bed",
        "4d29f892-7e25-4efa-ad0b-f348bd0fc0fc",
        "cce7b290-8a08-4904-a6c7-2b6613877cf5",
        "b3c5bc5d-1eae-4586-b9bd-db8486e6b689",
    ]
    enable_email_activity_capture: bool = False
    enable_meeting_domain_crm_association: bool = False
    enable_patch_meeting_pipeline_account_update: bool = False
    enable_patch_meeting_pipeline_account_update_org_ids: list[str] = []

    # Import Job configuration
    import_job_max_global_num_concurrent: int = 3
    import_job_grace_time_started_check_sec: int = 10 * 60  # 10 minutes

    # Posthog
    posthog_api_key: SecretStr = SecretStr(
        "phc_oZay5WydVxRGAhTkguXb1nIVD256fz5wN8t3dv3Dy5K"
    )
    # Keys <NAME_EMAIL> account
    posthog_personal_api_key: SecretStr = SecretStr(
        "phx_HBMKtMkLjjuVolcp7B5T9CH18VWDE9Ei5A0GCKF10jiBcEo"
    )
    posthog_poll_interval: int = 30
    posthog_disabled: bool = False

    enable_task_sequence_fanout: bool = False

    enable_core_plan_quota_policy: bool = False
    enable_core_plan_quota_policy_org_ids: list[str] = []

    enable_filterspec_perms: bool = False
    enable_filterspec_perms_org_ids: list[str] = []

    # ---------------
    #  Superset
    # ---------------
    superset_guest_token_jwt_secret: str = "superset-guest-token"
    superset_guest_token_jwt_ttl: int = 604800
    superset_guest_token_jwt_algorithm: str = "HS256"
    superset_guest_token_jwt_issuer: str = "http://localhost"
    superset_guest_token_jwt_audience: str = "http://localhost"
    superset_dashboard_id: str = "dashboard_id"
    superset_superuser_ids: list[str] = [
        "bff6d669-ffd8-4551-a4c7-6de94d133779"
    ]  # superuser who can access all data (no rls_rules)

    langfuse_secret_key: SecretStr = SecretStr(
        "sk-lf-6f8f3f4b-fa84-4039-bf9b-9313430a12db"
    )
    langfuse_public_key: SecretStr = SecretStr(
        "pk-lf-0bc80930-bed2-4951-b2fe-8bd0d2cf201b"
    )
    langfuse_host: str = "https://us.cloud.langfuse.com"

    # Gemini API Key
    gemini_api_key: SecretStr = SecretStr("AIzaSyDPTDHTDcTc02SVVh0LUcn7YZQbDkb0p48")

    # Elasticsearch
    es_cloud_id: SecretStr | None = None
    es_api_key: SecretStr | None = None
    es_dev_only_host: str = "http://localhost:9200"
    search_index_prefix: Literal["dev", "prod", "ci"] | str = "ci"
    enable_search_indexing: bool = False
    es_max_search_results_size: int = 20
    es_search_results_exclude_fields: list[str] = [
        "research_content",
        "sentences_embedding",
    ]

    # Knock
    knock_api_key: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")
    knock_base_url: str = "https://api.knock.app/v1"

    # Super admin email
    super_admin_email: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")

    # ----------------
    #  LiteLLM Client
    # ----------------
    litellm_company_info_summarization_max_tokens: int = 5000
    max_citation_output_tokens: int = 8192

    # ----------------
    #  Metrics / Tracing / Debugging
    # ----------------
    enable_meeting_service_debug: bool = False

    # ----------------
    #  List Query Optimization
    # ----------------
    enable_query_optimization: bool = False

    # ----------------
    # Research Agent
    # ----------------
    research_agent_blocked_org_ids: list[str] = ["b3c5bc5d-1eae-4586-b9bd-db8486e6b689"]
    research_agent_schedule_interval_days: int = 30
    research_agent_public_email_domains: list[str] = [
        "gmail.com",
        "yahoo.com",
        "hotmail.com",
        "outlook.com",
    ]
    research_agent_enable_scrappingdog_for_linkedin_url: bool = True
    research_agent_enable_scrapingdog_for_company_news: bool = False
    research_agent_dnr_domain_suffixes: list[str] = [
        ".fake"  # used as placeholder for empty domain during import
    ]
    research_agent_enable_find_linkedin_url_by_email: bool = True
    research_agent_enable_company_news: bool = True
    research_agent_enable_company_site_content: bool = True
    intel_hardcoded_user_id: str = "a1a1a1a1-b2b2-4c3c-d4d4-e5e5e5e5e5e5"
    research_agent_max_activities_per_second: int = (
        80  # defined based on external api concurrency
    )
    research_agent_max_activities_per_second_lowpri: int = (
        20  # lower capacity for low priority tasks
    )
    research_agent_enable_throttling: bool = False
    research_agent_enable_redirected_domain: bool = True

    # run timeout restricts the maximum time of a single workflow run,
    # we should terminate workflows after 7 days of running
    research_person_workflow_default_run_timeout: int = 7
    research_company_workflow_default_run_timeout: int = 7

    # if the latest post is older than this, we will fetch new posts.
    # default to 30 days.
    research_agent_company_posts_ttl: int = 30

    # if the latest news is older than this, we will fetch new news.
    # default to 1 week.
    research_agent_company_news_ttl: int = 7

    # if the latest person post is older than this, we will fetch new person posts.
    # default to 30 days.
    research_agent_person_posts_ttl: int = 30

    # if the updated date of the person info is older than this, we will fetch new person info.
    # default to 30 days.
    research_agent_person_info_ttl: int = 30

    # if the updated date of the company info is older than this, we will fetch new company info.
    # default to 30 days.
    research_agent_company_info_ttl: int = 30

    # ----------------
    #  Brightdata
    # ----------------
    brightdata_person_dataset_id: str = "gd_l1viktl72bvl7bjuj0"
    brightdata_company_dataset_id: str = "gd_l1vikfnt1wgvvqz95w"
    brightdata_post_dataset_id: str = "gd_lyy3tktm25m4avu764"

    # ----------------
    #  Scrapingdog
    # ----------------
    scrapingdog_base_url: str = "https://api.scrapingdog.com"
    scrapingdog_api_key: SecretStr = SecretStr("SETUP_IN_DOT_ENV_FOR_LOCAL")

    # Bot Assistant email patterns
    bot_assistant_email_prefix: str = "notetaker-dev+"
    bot_assistant_email_domain: str = "@reevo.ai"
    bot_assistant_nylas_grant_id: str = "cde2c625-e7b2-4dbf-9fc3-0cf25b3a7eac"

    # pipeline qualification properties
    enable_pipeline_qual_prop_during_creation: bool = False

    support_on_super_admin_check_in_auth: bool = False

    # ----------------
    #  User Feedback
    # ----------------
    enable_user_feedback: bool = True

    # ----------------
    #  Perf / Debug
    # ----------------
    enable_event_loop_debug: bool = False

    # Disable queue mode by default, as there is risk of running into deadlocks
    enable_loguru_queue_mode: bool = False

    # ----------------
    #  Unsubscription link
    # ----------------
    unsubscribe_link_base_url: str = "https://app.reevo.ai/public/unsubscribe"
    unsubscribe_link_html_id: str = "reevo-unsubscribe-link"

    enable_predicate_v2: bool = False

    def should_use_predicate_v2(self) -> bool:
        """
        Whether to use V2 predicate evaluator for filter evaluation.
        """
        return self.enable_predicate_v2 or self.environment != "prod"

    @field_validator("environment")
    @classmethod
    def is_valid_environment(cls, v: str) -> str:
        if v in {"local", "pytest", "dev", "staging", "prod"}:
            return v
        raise ValueError(f"Invalid environment: {v}")

    @property
    def db_url(self) -> URL:
        """Assemble database URL from settings.

        :return: database URL.
        """
        return URL.build(
            scheme="postgresql+asyncpg",
            host=self.db_host,
            port=self.db_port,
            user=self.db_user,
            password=self.db_pass,
            path=f"/{self.db_base}",
        )

    @property
    def dwh_url(self) -> URL:
        """Assemble PostgreSQL DWH URL from settings."""
        return URL.build(
            scheme="postgresql+asyncpg",
            host=self.dwh_host,
            port=self.dwh_port,
            user=self.dwh_user,
            password=self.dwh_pass,
            path=f"/{self.dwh_base}",
        )

    @property
    def db_url_for_migration(self) -> URL:
        """Assemble database URL from settings for migrations with migrator user / privileges.

        :return: database URL.
        """
        return URL.build(
            scheme="postgresql+asyncpg",
            host=self.db_host,
            port=self.db_port,
            user=self.db_migrator_user,
            password=self.db_migrator_pass,
            path=f"/{self.db_base}",
        )

    @property
    def redis_url(self) -> URL:
        """Assemble REDIS URL from settings.

        :return: redis URL.
        """
        path = ""
        if self.redis_base is not None:
            path = f"/{self.redis_base}"
        return URL.build(
            scheme="redis",
            host=self.redis_host,
            port=self.redis_port,
            user=self.redis_user,
            password=self.redis_pass,
            path=path,
        )

    model_config = SettingsConfigDict(
        env_file=".env",
        env_prefix="SALESTECH_BE_",
        env_file_encoding="utf-8",
    )


settings = Settings()


def get_settings() -> Settings:
    return Settings()
