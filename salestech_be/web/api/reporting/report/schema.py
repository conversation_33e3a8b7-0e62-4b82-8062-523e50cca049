from typing import Any
from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.reporting.type.layout import ReportLayoutConfig


class ReportResponse(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    dashboard_id: UUID
    dataset_id: UUID
    created_at: str
    updated_at: str | None = None
    published_at: str | None = None
    layout_config: ReportLayoutConfig | None = None


class ReportDetailResponse(ReportResponse):
    pass


class ReportListResponse(BaseModel):
    reports: list[ReportResponse]
    total: int
    page: int
    page_size: int


class CreateReportRequest(BaseModel):
    name: str
    description: str | None = None
    dashboard_id: UUID
    dataset_id: UUID
    layout_config: ReportLayoutConfig | None = None


class UpdateReportRequest(BaseModel):
    name: str | None = None
    description: str | None = None
    layout_config: ReportLayoutConfig | None = None


class PublishReportRequest(BaseModel):
    """Request to publish a report (sets published_at to current timestamp)"""
    pass


class UnpublishReportRequest(BaseModel):
    """Request to unpublish a report (sets published_at to null)"""
    pass


class ReportQueryRequest(BaseModel):
    filters: dict[str, Any] | None = None
