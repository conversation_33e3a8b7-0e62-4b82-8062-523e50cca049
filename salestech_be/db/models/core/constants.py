from salestech_be.util.enum_util import NameValueStrEnum


class TableName(NameValueStrEnum):
    unknown = "unknown"

    # actual db table names, sorted
    account = "account"
    account_engagement = "account_engagement"
    activity_v2 = "activity_v2"
    activity_sub_reference = "activity_sub_reference"
    address = "address"
    api_key = "api_key"
    attachment = "attachment"
    audience_list = "audience_list"
    audience_list_membership = "audience_list_membership"
    auth0_user = "auth0_user"
    calendar_account = "calendar_account"
    approval_request = "approval_request"
    call = "call"
    call_stats = "call_stats"
    chat = "chat"
    chat_message = "chat_message"
    citation = "citation"
    company = "company"
    cfield_association = "cfield_association"
    cfield_metadata = "cfield_metadata"
    cobject_data = "cobject_data"
    cobject_index = "cobject_index"
    cobject_metadata = "cobject_metadata"
    comment = "comment"
    contact = "contact"
    contact_email = "contact_email"
    contact_email_account_association = "contact_email_account_association"
    contact_phone_number = "contact_phone_number"
    contact_phone_number_account_association = (
        "contact_phone_number_account_association"
    )
    contact_account_association = "contact_account_association"
    contact_account_engagement_association = "contact_account_engagement_association"
    contact_pipeline_association = "contact_pipeline_association"
    contact_deal = "contact_deal"
    contact_sourcing_queue = "contact_sourcing_queue"
    contact_sourcing_queue_association = "contact_sourcing_queue_association"
    contact_sourcing_queue_filter_history = "contact_sourcing_queue_filter_history"
    conversation = "conversation"
    criteria_item_v2 = "criteria_item_v2"
    stage_exit_criteria_v2 = "stage_exit_criteria_v2"
    stage_list_entrance_criteria_v2 = "stage_list_entrance_criteria_v2"
    crm_integrity_job = "crm_integrity_job"
    crm_integrity_subdomain_job = "crm_integrity_subdomain_job"
    crm_integrity_operation = "crm_integrity_operation"
    crm_integrity_associated_entity_operation = (
        "crm_integrity_associated_entity_operation"
    )
    crm_object_ai_recs = "crm_object_ai_recs"
    crm_property_ai_recs = "crm_property_ai_recs"
    crm_property_metadata = "crm_property_metadata"
    crm_sync_instance = "crm_sync_instance"
    crm_sync_object = "crm_sync_object"
    crm_sync_record = "crm_sync_record"
    custom_object_association = "custom_object_association"
    custom_object_association_audit_log = "custom_object_association_audit_log"
    custom_object_association_record = "custom_object_association_record"
    deal = "deal"
    deal_funnel = "deal_funnel"
    deal_stage_select_list_metadata = "deal_stage_select_list_metadata"
    deal_stage_select_list_value_metadata = "deal_stage_select_list_value_metadata"
    domain_health = "domain_health"
    domain_object_list = "domain_object_list"
    domain_object_list_item = "domain_object_list_item"
    domain_crm_association = "domain_crm_association"
    email_account = "email_account"
    email_account_health = "email_account_health"
    email_account_health_history = "email_account_health_history"
    email_account_pool = "email_account_pool"
    email_account_pool_membership = "email_account_pool_membership"
    email_account_slot_allocation = "email_account_slot_allocation"
    email_account_warmup = "email_account_warmup"
    email_event = "email_event"
    email_account_warm_up_campaign = "email_account_warm_up_campaign"
    email_template = "email_template"
    email_template_history = "email_template_history"
    email_template_category = "email_template_category"
    event_schedule = "event_schedule"
    event_schedule_booking = "event_schedule_booking"
    event_tracking = "event_tracking"
    event_tracking_history = "event_tracking_history"
    extraction_config_feature_link = "extraction_config_feature_link"
    extraction_field = "extraction_field"
    extraction_section = "extraction_section"
    external_sync_cdc_event = "external_sync_cdc_event"
    form = "form"
    form_submission = "form_submission"
    global_thread = "global_thread"
    global_message = "global_message"
    global_message_association = "global_message_association"
    global_thread_messages_association = "global_thread_messages_association"
    import_csv_job = "import_csv_job"
    import_csv_job_review = "import_csv_job_review"
    import_record = "import_record"
    insight = "insight"
    intel_provider = "intel_provider"
    intel_company = "intel_company"
    intel_person = "intel_person"
    intel_company_association = "intel_company_association"
    intel_person_association = "intel_person_association"
    intel_company_info = "intel_company_info"
    intel_company_info_history = "intel_company_info_history"
    intel_company_activity = "intel_company_activity"
    intel_person_info = "intel_person_info"
    intel_person_info_history = "intel_person_info_history"
    intel_person_activity = "intel_person_activity"
    insight_section = "insight_section"
    job = "job"
    job_role = "job_role"
    job_role_user_association = "job_role_user_association"
    lead = "lead"
    lead_stage_select_list_metadata = "lead_stage_select_list_metadata"
    lead_stage_select_list_value_metadata = "lead_stage_select_list_value_metadata"
    linkedin_person = "linkedin_person"
    linkedin_company = "linkedin_company"
    live_transcript_session = "live_transcript_session"
    marketing_account = "marketing_account"
    meeting = "meeting"
    meeting_annotation = "meeting_annotation"
    meeting_bot = "meeting_bot"
    meeting_stats = "meeting_stats"
    meeting_clip = "meeting_clip"
    meeting_share = "meeting_share"
    meeting_share_verification = "meeting_share_verification"
    meeting_view = "meeting_view"
    message = "message"
    message_metadata = "message_metadata"
    note = "note"
    note_reference = "note_reference"
    notification = "notification"
    oauth_provider = "oauth_provider"
    oauth_user_auth = "oauth_user_auth"
    object_list_view_schema = "object_list_view_schema"
    onboarding_progress = "onboarding_progress"
    organization_phone_number = "organization_phone_number"
    outbound_workspace = "outbound_workspace"
    outbound_domain = "outbound_domain"
    organization = "organization"
    organization_info = "organization_info"
    organization_preference = "organization_preference"
    organization_sales_methodology = "organization_sales_methodology"
    organization_external_sync = "organization_external_sync"
    organization_view_schema_preference_config = (
        "organization_view_schema_preference_config"
    )
    user_organization_preference = "user_organization_preference"
    pdl_company = "pdl_company"
    pdl_person = "pdl_person"
    person = "person"
    permission_set = "permission_set"
    permission_set_group = "permission_set_group"
    permission_set_group_permission_set_association = (
        "permission_set_group_permission_set_association"
    )
    permission_set_group_user_association = "permission_set_group_user_association"
    permission_set_user_association = "permission_set_user_association"
    phone_number = "phone_number"
    pipeline = "pipeline"
    pipeline_intel = "pipeline_intel"
    pipeline_qualification_property = "pipeline_qualification_property"
    pipeline_qualification_property_stage_snapshot = (
        "pipeline_qualification_property_stage_snapshot"
    )
    pipeline_stage_select_list_metadata = "pipeline_stage_select_list_metadata"
    pipeline_stage_select_list_value_metadata = (
        "pipeline_stage_select_list_value_metadata"
    )
    pipeline_tracking = "pipeline_tracking"
    prompt_context = "prompt_context"
    prompt_template = "prompt_template"
    prompt = "prompt"
    propagation_rule = "propagation_rule"
    prospecting_tag = "prospecting_tag"
    prospecting_run = "prospecting_run"
    prospecting_run_result = "prospecting_run_result"
    quota_policy = "quota_policy"
    quota_usage = "quota_usage"
    reporting_dashboard = "reporting_dashboard"
    reporting_dashboard_report_association = "reporting_dashboard_report_association"
    reporting_dataset = "reporting_dataset"
    reporting_dataset_field = "reporting_dataset_field"
    reporting_dataset_relation = "reporting_dataset_relation"
    reporting_report = "reporting_report"
    saved_domain_object_filter = "saved_domain_object_filter"
    search_query = "search_query"
    select_list = "select_list"
    select_list_value = "select_list_value"
    select_list_custom_field_association = "select_list_custom_field_association"
    sequence = "sequence"
    sequence_action = "sequence_action"
    sequence_execution = "sequence_execution"
    # TODO(REEVO-212) remove tables
    sequence_list = "sequence_list"
    sequence_list_membership = "sequence_list_membership"
    sequence_schedule = "sequence_schedule"
    sequence_step = "sequence_step"
    sequence_target = "sequence_target"
    sequence_v2 = "sequence_v2"
    sequence_step_v2 = "sequence_step_v2"
    sequence_step_variant = "sequence_step_variant"
    sequence_enrollment = "sequence_enrollment"
    sequence_step_execution = "sequence_step_execution"
    sequence_enrollment_step_variant_association = (
        "sequence_enrollment_step_variant_association"
    )
    sequence_enrollment_contact = "sequence_enrollment_contact"
    sequence_enrollment_run = "sequence_enrollment_run"
    signature = "signature"
    stage_criteria_condition = "stage_criteria_condition"
    task = "task"
    task_reference = "task_reference"
    task_template = "task_template"
    template = "template"
    thread = "thread"
    tracker = "tracker"
    tracker_stats = "tracker_stats"
    transcript = "transcript"
    twilio_call = "twilio_call"
    twilio_call_status_event = "twilio_call_status_event"
    twilio_call_tree = "twilio_call_tree"
    twilio_phone_number = "twilio_phone_number"
    twilio_subaccount = "twilio_subaccount"
    twilio_verified_phone_number = "twilio_verified_phone_number"
    twilio_voice_session = "twilio_voice_session"
    unsubscription_group = "unsubscription_group"
    unsubscription_group_recipient = "unsubscription_group_recipient"
    user = "user"
    user_calendar = "user_calendar"
    user_calendar_event = "user_calendar_event"
    user_feedback = "user_feedback"
    user_goal = "user_goal"
    user_phone_number = "user_phone_number"
    event_schedule_host = "event_schedule_host"
    user_integration = "user_integration"
    user_integration_connector = "user_integration_connector"
    user_notification = "user_notification"
    user_organization = "user_organization"
    user_organization_association = "user_organization_association"
    user_platform_credential = "user_platform_credential"
    user_view_schema_preference_config = "user_view_schema_preference_config"
    user_invite = "user_invite"
    vital_filter = "vital_filter"
    vital_score = "vital_score"
    vital_score_filter_result = "vital_score_filter_result"
    voice_provider_account = "voice_provider_account"
    warmup_health = "warmup_health"
    workflow = "workflow"
    workflow_block = "workflow_block"
    workflow_snapshot = "workflow_snapshot"
    workflow_node = "workflow_node"
    workflow_edge = "workflow_edge"
    workflow_run = "workflow_run"
    workflow_run_node = "workflow_run_node"
    workflow_trigger_event = "workflow_trigger_event"
    pdl_cache = "pdl_cache"
