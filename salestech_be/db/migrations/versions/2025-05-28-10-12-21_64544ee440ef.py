"""reporting dataset tables

Revision ID: 64544ee440ef
Revises: 94f0e00d88ce
Create Date: 2025-05-22 10:12:21.433093+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "64544ee440ef"
down_revision: str | tuple[str, ...] | None = "94f0e00d88ce"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    # Create Dataset table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dataset (
                "id" UUID PRIMARY KEY,
                "name" TEXT NOT NULL,
                "description" TEXT,
                "source" TEXT NOT NULL,
                "type" TEXT NOT NULL,
                "table_reference" TEXT,
                "query_config" JSONB,
                "sql_statement" TEXT,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create DatasetField table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dataset_field (
                "id" UUID PRIMARY KEY,
                "dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "name" TEXT NOT NULL,
                "display_name" TEXT,
                "data_type" TEXT NOT NULL,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create DatasetLineage table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dataset_lineage (
                "id" UUID PRIMARY KEY,
                "lineage_type" TEXT NOT NULL,
                "parent_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "parent_field_id" UUID REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,
                "child_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "child_field_id" UUID REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create DatasetRelation table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dataset_relation (
                "id" UUID PRIMARY KEY,
                "source_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "target_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "source_field_id" UUID NOT NULL REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,
                "target_field_id" UUID NOT NULL REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,
                "join_type" TEXT NOT NULL,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create Function table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_function (
                "id" UUID PRIMARY KEY,
                "version" INTEGER NOT NULL,
                "func_name" TEXT NOT NULL,
                "display_name" TEXT NOT NULL,
                "example" TEXT,
                "description" TEXT,
                "return_type" TEXT NOT NULL,
                "is_active" BOOLEAN NOT NULL DEFAULT TRUE,

                "created_at" TIMESTAMPTZ NOT NULL,
                "updated_at" TIMESTAMPTZ,
                "deleted_at" TIMESTAMPTZ
            );
        """,
    )

    # Create FunctionParameter table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_function_parameter (
                "id" UUID PRIMARY KEY,
                "function_id" UUID NOT NULL REFERENCES reporting_function(id),
                "param_order" INTEGER NOT NULL,
                "param_type" TEXT NOT NULL,
                "is_required" BOOLEAN NOT NULL DEFAULT FALSE,
                "options" JSONB,

                "created_at" TIMESTAMPTZ NOT NULL,
                "updated_at" TIMESTAMPTZ,
                "deleted_at" TIMESTAMPTZ
            );
        """,
    )

    # Create Report table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_report (
                "id" UUID PRIMARY KEY,
                "name" TEXT NOT NULL,
                "description" TEXT,
                "dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "layout_config" JSONB,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create Dashboard table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dashboard (
                "id" UUID PRIMARY KEY,
                "name" TEXT NOT NULL,
                "description" TEXT,
                "layout_config" JSONB,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create DashboardReportAssociation table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dashboard_report_association (
                "id" UUID PRIMARY KEY,
                "dashboard_id" UUID NOT NULL REFERENCES reporting_dashboard(id) ON DELETE CASCADE,
                "report_id" UUID NOT NULL REFERENCES reporting_report(id) ON DELETE CASCADE,
                "layout_config" JSONB,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )


def downgrade() -> None:
    op.execute(
        sqltext="""
            DROP TABLE reporting_dashboard_report_association;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dashboard;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_report;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_function_parameter;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_function;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dataset_relation;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dataset_lineage;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dataset_field;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dataset;
        """
    )
