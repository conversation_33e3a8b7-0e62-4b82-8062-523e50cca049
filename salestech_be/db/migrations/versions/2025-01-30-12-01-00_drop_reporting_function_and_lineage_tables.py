"""drop reporting function and lineage tables

Revision ID: drop_reporting_function_and_lineage_tables
Revises: add_published_at_to_reporting_report
Create Date: 2025-01-30 12:01:00.000000+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "drop_reporting_function_and_lineage_tables"
down_revision: str | tuple[str, ...] | None = "add_published_at_to_reporting_report"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    # Drop function parameter table first (has foreign key to function table)
    op.execute(
        """
        DROP TABLE IF EXISTS reporting_function_parameter;
        """
    )
    
    # Drop function table
    op.execute(
        """
        DROP TABLE IF EXISTS reporting_function;
        """
    )
    
    # Drop dataset lineage table
    op.execute(
        """
        DROP TABLE IF EXISTS reporting_dataset_lineage;
        """
    )


def downgrade() -> None:
    # Recreate function table
    op.execute(
        """
        CREATE TABLE IF NOT EXISTS reporting_function (
            "id" UUID PRIMARY KEY,
            "version" INTEGER NOT NULL,
            "func_name" TEXT NOT NULL,
            "display_name" TEXT NOT NULL,
            "example" TEXT,
            "description" TEXT,
            "return_type" TEXT NOT NULL,
            "is_active" BOOLEAN NOT NULL DEFAULT TRUE,

            "created_at" TIMESTAMPTZ NOT NULL,
            "updated_at" TIMESTAMPTZ,
            "deleted_at" TIMESTAMPTZ
        );
        """
    )
    
    # Recreate function parameter table
    op.execute(
        """
        CREATE TABLE IF NOT EXISTS reporting_function_parameter (
            "id" UUID PRIMARY KEY,
            "function_id" UUID NOT NULL REFERENCES reporting_function(id),
            "param_order" INTEGER NOT NULL,
            "param_type" TEXT NOT NULL,
            "is_required" BOOLEAN NOT NULL DEFAULT FALSE,
            "options" JSONB,

            "created_at" TIMESTAMPTZ NOT NULL,
            "updated_at" TIMESTAMPTZ,
            "deleted_at" TIMESTAMPTZ
        );
        """
    )
    
    # Recreate dataset lineage table
    op.execute(
        """
        CREATE TABLE IF NOT EXISTS reporting_dataset_lineage (
            "id" UUID PRIMARY KEY,
            "lineage_type" TEXT NOT NULL,
            "parent_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
            "parent_field_id" UUID REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,
            "child_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
            "child_field_id" UUID REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,

            "organization_id" UUID,
            "created_at" TIMESTAMPTZ NOT NULL,
            "created_by_user_id" UUID,
            "updated_at" TIMESTAMPTZ,
            "updated_by_user_id" UUID,
            "deleted_at" TIMESTAMPTZ,
            "deleted_by_user_id" UUID
        );
        """
    )
